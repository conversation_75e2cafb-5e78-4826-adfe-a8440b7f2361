#!/bin/bash

# Kubernetes User Management Tool
# ===============================
# This script follows Kubernetes RBAC best practices as documented at:
# https://kubernetes.io/docs/reference/access-authn-authz/rbac/
#
# Key Features:
# - Works exclusively with the 'viewer-role' Role for read-only access
# - Creates namespace-scoped RoleBindings AND manages ClusterRoleBinding
# - Filters user discovery to show only User resources (excludes ServiceAccounts)
# - Follows the principle of least privilege
#
# RBAC Configuration:
# - Role: viewer-role (must exist in target namespaces)
# - RoleBinding pattern: {username}-access
# - ClusterRoleBinding: ns-viewer-cluster-binding (for cluster-wide access)
# - Scope: Namespace-level + cluster-wide permissions

# Predefined configuration
PREDEFINED_NAMESPACES=("app-dev" "app-stage" "app")
DEFAULT_ROLE="viewer-role"  # Default role name in each namespace (viewer-role for read-only access)
CLUSTER_ROLE_BINDING="ns-viewer-cluster-binding"  # ClusterRoleBinding for cluster-wide viewer access

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
if ! command_exists kubectl; then
    echo "Error: kubectl is not installed or not in PATH"
    exit 1
fi

if ! command_exists openssl; then
    echo "Error: openssl is not installed"
    exit 1
fi

# Check for jq (optional but recommended for ClusterRoleBinding management)
JQ_AVAILABLE=false
if command_exists jq; then
    JQ_AVAILABLE=true
fi

# Global variables
USERNAME=""
VALID_NAMESPACES=()

# Colors for better UX
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print section headers
print_header() {
    local title=$1
    echo
    print_color "$CYAN" "=================================="
    print_color "$CYAN" "$title"
    print_color "$CYAN" "=================================="
    echo
}

# Function to check if user exists
check_user_exists() {
    local username="$1"

    # Check if CSR exists
    if kubectl get csr "${username}-csr" >/dev/null 2>&1; then
        return 0  # User exists
    fi

    # Check if certificate files exist
    if [ -f "user-certs/${username}.crt" ] && [ -f "user-certs/${username}.key" ]; then
        return 0  # User exists
    fi

    return 1  # User doesn't exist
}

# Function to check if user has access to namespaces
check_user_namespace_access() {
    local username="$1"
    local -n namespaces_ref=$2
    local existing_namespaces=()

    for ns in "${namespaces_ref[@]}"; do
        if kubectl get rolebinding -n "$ns" "${username}-access" >/dev/null 2>&1; then
            existing_namespaces+=("$ns")
        fi
    done

    if [ ${#existing_namespaces[@]} -gt 0 ]; then
        echo "User '$username' already has access to namespaces:"
        printf '  - %s\n' "${existing_namespaces[@]}"
        return 0
    fi

    return 1
}

# Function to check if user exists in ClusterRoleBinding
check_user_in_cluster_binding() {
    local username="$1"

    if ! kubectl get clusterrolebinding "$CLUSTER_ROLE_BINDING" >/dev/null 2>&1; then
        return 1  # ClusterRoleBinding doesn't exist
    fi

    local subjects
    subjects=$(kubectl get clusterrolebinding "$CLUSTER_ROLE_BINDING" -o jsonpath='{.subjects[?(@.kind=="User")].name}' 2>/dev/null)

    if [[ " $subjects " =~ " $username " ]]; then
        return 0  # User exists in ClusterRoleBinding
    fi

    return 1  # User doesn't exist in ClusterRoleBinding
}

# Function to add user to ClusterRoleBinding
add_user_to_cluster_binding() {
    local username="$1"

    # Check if ClusterRoleBinding exists
    if ! kubectl get clusterrolebinding "$CLUSTER_ROLE_BINDING" >/dev/null 2>&1; then
        print_color "$YELLOW" "⚠️  ClusterRoleBinding '$CLUSTER_ROLE_BINDING' does not exist"
        print_color "$BLUE" "   Skipping cluster-wide access configuration"
        return 1
    fi

    # Check if user is already in the binding
    if check_user_in_cluster_binding "$username"; then
        print_color "$YELLOW" "⚠️  User '$username' already exists in ClusterRoleBinding '$CLUSTER_ROLE_BINDING'"
        return 0
    fi

    # Add user to ClusterRoleBinding
    kubectl patch clusterrolebinding "$CLUSTER_ROLE_BINDING" --type='json' \
        -p="[{\"op\": \"add\", \"path\": \"/subjects/-\", \"value\": {\"kind\": \"User\", \"name\": \"$username\", \"apiGroup\": \"rbac.authorization.k8s.io\"}}]" \
        >/dev/null 2>&1

    if [[ $? -eq 0 ]]; then
        print_color "$GREEN" "✅ Added user '$username' to ClusterRoleBinding '$CLUSTER_ROLE_BINDING'"
        return 0
    else
        print_color "$RED" "❌ Failed to add user '$username' to ClusterRoleBinding '$CLUSTER_ROLE_BINDING'"
        return 1
    fi
}

# Function to remove user from ClusterRoleBinding
remove_user_from_cluster_binding() {
    local username="$1"

    # Check if ClusterRoleBinding exists
    if ! kubectl get clusterrolebinding "$CLUSTER_ROLE_BINDING" >/dev/null 2>&1; then
        print_color "$YELLOW" "⚠️  ClusterRoleBinding '$CLUSTER_ROLE_BINDING' does not exist"
        return 1
    fi

    # Check if user exists in the binding
    if ! check_user_in_cluster_binding "$username"; then
        print_color "$YELLOW" "⚠️  User '$username' not found in ClusterRoleBinding '$CLUSTER_ROLE_BINDING'"
        return 0
    fi

    if [[ "$JQ_AVAILABLE" == true ]]; then
        # Use jq for precise JSON manipulation
        local current_subjects
        current_subjects=$(kubectl get clusterrolebinding "$CLUSTER_ROLE_BINDING" -o json | \
            jq --arg username "$username" '.subjects | map(select(.kind != "User" or .name != $username))')

        # Update ClusterRoleBinding with filtered subjects
        kubectl patch clusterrolebinding "$CLUSTER_ROLE_BINDING" --type='json' \
            -p="[{\"op\": \"replace\", \"path\": \"/subjects\", \"value\": $current_subjects}]" \
            >/dev/null 2>&1
    else
        # Fallback: recreate ClusterRoleBinding without the user
        print_color "$YELLOW" "⚠️  jq not available, using fallback method"

        # Get current ClusterRoleBinding details
        local role_ref=$(kubectl get clusterrolebinding "$CLUSTER_ROLE_BINDING" -o jsonpath='{.roleRef.name}')
        local role_kind=$(kubectl get clusterrolebinding "$CLUSTER_ROLE_BINDING" -o jsonpath='{.roleRef.kind}')

        # Get all subjects except the user to remove
        local other_subjects=$(kubectl get clusterrolebinding "$CLUSTER_ROLE_BINDING" \
            -o jsonpath='{range .subjects[*]}{.kind}{":"}{.name}{" "}{end}' | \
            tr ' ' '\n' | grep -v "^User:$username$" | tr '\n' ' ')

        # Delete and recreate the ClusterRoleBinding
        kubectl delete clusterrolebinding "$CLUSTER_ROLE_BINDING" >/dev/null 2>&1

        # Recreate with remaining subjects
        local create_cmd="kubectl create clusterrolebinding $CLUSTER_ROLE_BINDING --clusterrole=$role_ref"
        for subject in $other_subjects; do
            local kind=$(echo "$subject" | cut -d: -f1)
            local name=$(echo "$subject" | cut -d: -f2)
            if [[ "$kind" == "User" ]]; then
                create_cmd="$create_cmd --user=$name"
            elif [[ "$kind" == "ServiceAccount" ]]; then
                create_cmd="$create_cmd --serviceaccount=$name"
            elif [[ "$kind" == "Group" ]]; then
                create_cmd="$create_cmd --group=$name"
            fi
        done

        eval "$create_cmd" >/dev/null 2>&1
    fi

    if [[ $? -eq 0 ]]; then
        print_color "$GREEN" "✅ Removed user '$username' from ClusterRoleBinding '$CLUSTER_ROLE_BINDING'"
        return 0
    else
        print_color "$RED" "❌ Failed to remove user '$username' from ClusterRoleBinding '$CLUSTER_ROLE_BINDING'"
        return 1
    fi
}

# Function to display main menu
show_main_menu() {
    clear
    print_header "Kubernetes User Management Tool"

    print_color "$BLUE" "Please select an operation:"
    echo
    echo "  1) Create new user"
    echo "  2) Export kubeconfig for existing user"
    echo "  3) Modify user access (RoleBindings)"
    echo "  4) List existing users"
    echo "  5) Delete user"
    echo "  6) Show cluster info (debug)"
    echo "  7) Exit"
    echo
}

# Function to get user input for menu selection
get_menu_choice() {
    local choice
    while true; do
        read -p "Enter your choice (1-7): " choice
        case $choice in
            [1-7])
                echo "$choice"
                return 0
                ;;
            *)
                print_color "$RED" "❌ Invalid choice. Please enter a number between 1 and 7."
                ;;
        esac
    done
}

# Function to get username input
get_username() {
    local prompt="$1"
    local username

    while true; do
        read -p "$prompt" username
        if [[ -n "$username" && "$username" =~ ^[a-zA-Z0-9][a-zA-Z0-9._-]*$ ]]; then
            echo "$username"
            return 0
        else
            print_color "$RED" "❌ Invalid username. Use alphanumeric characters, dots, hyphens, and underscores only."
        fi
    done
}

# Function to confirm destructive operations
confirm_operation() {
    local message="$1"
    local response

    print_color "$YELLOW" "⚠️  $message"
    read -p "Type 'yes' to confirm: " response

    if [[ "$response" == "yes" ]]; then
        return 0
    else
        print_color "$BLUE" "Operation cancelled."
        return 1
    fi
}

# Function to get and validate namespace selection
get_namespace_selection() {
    local operation_type="$1"  # "grant" or "revoke"
    local username="$2"

    # Display available namespaces
    echo
    print_color "$BLUE" "Available namespaces:"
    printf '  - %s\n' "${PREDEFINED_NAMESPACES[@]}"

    # Get namespace input
    local input_namespaces
    read -p $'\nEnter namespaces (comma separated): ' input_namespaces
    IFS=',' read -ra NAMESPACES <<< "$input_namespaces"

    # Validate namespaces
    VALID_NAMESPACES=()
    for ns in "${NAMESPACES[@]}"; do
        ns=$(echo "$ns" | xargs)  # Trim whitespace

        # Check if namespace is in predefined list
        if [[ ! " ${PREDEFINED_NAMESPACES[*]} " =~ " ${ns} " ]]; then
            print_color "$YELLOW" "⚠️  Skipping invalid namespace '$ns' - not in predefined list"
            continue
        fi

        # Check if namespace exists in cluster
        if ! kubectl get namespace "$ns" >/dev/null 2>&1; then
            print_color "$YELLOW" "⚠️  Namespace '$ns' does not exist in cluster"
            continue
        fi

        # For revoke operations, check if user has access
        if [[ "$operation_type" == "revoke" ]]; then
            if ! kubectl get rolebinding -n "$ns" "${username}-access" >/dev/null 2>&1; then
                print_color "$YELLOW" "⚠️  User '$username' doesn't have access to namespace '$ns'"
                continue
            fi
        fi

        # Check if viewer-role exists
        if ! kubectl get role -n "$ns" "$DEFAULT_ROLE" >/dev/null 2>&1; then
            print_color "$YELLOW" "⚠️  Required role '$DEFAULT_ROLE' not found in namespace '$ns'"
            print_color "$BLUE"   "   This script works exclusively with the '$DEFAULT_ROLE' Role for read-only access"
            continue
        fi

        VALID_NAMESPACES+=("$ns")
    done

    if [ ${#VALID_NAMESPACES[@]} -eq 0 ]; then
        print_color "$RED" "❌ No valid namespaces selected"
        return 1
    fi

    echo
    print_color "$GREEN" "Selected namespaces:"
    printf '  - %s\n' "${VALID_NAMESPACES[@]}"
    return 0
}

# Function to list existing users
# ------------------------------------------------------------------
# list_existing_users – show only objects of kind: User when possible
# ------------------------------------------------------------------
list_existing_users() {
  print_header "Existing Users (kind: User)"

  local found_users=false
  local user_api_available=false

  # 1) Try to list the *real* User resource -------------------------
  if kubectl api-resources --api-group=authentication.k8s.io \
     | grep -q '^users'; then
     user_api_available=true
  fi

  if [[ "$user_api_available" == true ]]; then
    local users
    users=$(kubectl get users -o custom-columns=NAME:.metadata.name --no-headers 2>/dev/null)
    if [[ -n "$users" ]]; then
      found_users=true
      print_color "$GREEN" "Users defined in cluster:"
      while IFS= read -r u; do
        [[ -z "$u" ]] && continue
        echo "  👤 $u"
      done <<<"$users"
    else
      print_color "$YELLOW" "No 'User' objects found in the cluster."
    fi
  else
    # 2) Fallback – heuristic inspection (filtered for User kind only) -
    print_color "$YELLOW" "⚠️  The 'User' API resource is not exposed on this cluster."
    print_color "$BLUE"  "   Falling back to heuristic discovery (User kind only)..."

    # --- CSR section (filter for user CSRs only) -------------------
    print_color "$BLUE" "🔍 Checking for User Certificate Signing Requests..."
    local all_csrs
    all_csrs=$(kubectl get csr --no-headers 2>/dev/null)
    if [[ -n "$all_csrs" ]]; then
      echo "$all_csrs" | while read -r line; do
        local csr_name status age
        csr_name=$(echo "$line" | awk '{print $1}')
        status=$(echo "$line" | awk '{print $6}')
        age=$(echo "$line" | awk '{print $4}')

        # Filter out system CSRs and ServiceAccount CSRs
        if [[ "$csr_name" =~ ^system: ]] || [[ "$csr_name" =~ serviceaccount ]]; then
          continue
        fi

        case "$status" in
          Approved,Issued) echo "  ✅ $csr_name (Approved, Age: $age)" ;;
          Approved)        echo "  🟡 $csr_name (Approved but not issued, Age: $age)" ;;
          *)               echo "  ❌ $csr_name ($status, Age: $age)" ;;
        esac
        found_users=true
      done
    fi

    # --- Collect all users and their access information --------
    print_color "$BLUE" "🔍 Collecting user access information..."

    # Create associative arrays to track user access
    declare -A user_rolebindings
    declare -A user_cluster_access
    local all_users=()

    # Get RoleBinding information (filter for viewer-role only)
    local user_bindings
    user_bindings=$(kubectl get rolebinding -A \
                   -o jsonpath='{range .items[*]}{.metadata.namespace}{"\t"}{.metadata.name}{"\t"}{.roleRef.name}{"\t"}{range .subjects[?(@.kind=="User")]}{.name}{" "}{end}{"\n"}{end}' \
                   2>/dev/null | grep -E "\s${DEFAULT_ROLE}\s" | grep -v "^$")

    if [[ -n "$user_bindings" ]]; then
      while IFS=$'\t' read -r namespace binding_name role_name users; do
        [[ -z "$users" ]] && continue
        for user in $users; do
          [[ -z "$user" ]] && continue
          if [[ ! " ${all_users[*]} " =~ " ${user} " ]]; then
            all_users+=("$user")
          fi
          if [[ -n "${user_rolebindings[$user]}" ]]; then
            user_rolebindings[$user]="${user_rolebindings[$user]}, $namespace/$binding_name"
          else
            user_rolebindings[$user]="$namespace/$binding_name"
          fi
        done
      done <<<"$user_bindings"
    fi

    # Get ClusterRoleBinding information
    if kubectl get clusterrolebinding "$CLUSTER_ROLE_BINDING" >/dev/null 2>&1; then
      local cluster_users
      cluster_users=$(kubectl get clusterrolebinding "$CLUSTER_ROLE_BINDING" \
                     -o jsonpath='{.subjects[?(@.kind=="User")].name}' 2>/dev/null)
      if [[ -n "$cluster_users" ]]; then
        for user in $cluster_users; do
          [[ -z "$user" ]] && continue
          if [[ ! " ${all_users[*]} " =~ " ${user} " ]]; then
            all_users+=("$user")
          fi
          user_cluster_access[$user]="$CLUSTER_ROLE_BINDING"
        done
      fi
    fi

    # Display consolidated user information
    if [[ ${#all_users[@]} -gt 0 ]]; then
      found_users=true
      print_color "$GREEN" "Users with ${DEFAULT_ROLE} access:"
      for user in "${all_users[@]}"; do
        local access_info=""

        # Add RoleBinding information
        if [[ -n "${user_rolebindings[$user]}" ]]; then
          access_info="RoleBindings: ${user_rolebindings[$user]}"
        fi

        # Add ClusterRoleBinding information
        if [[ -n "${user_cluster_access[$user]}" ]]; then
          if [[ -n "$access_info" ]]; then
            access_info="$access_info; ClusterRoleBinding: ${user_cluster_access[$user]}"
          else
            access_info="ClusterRoleBinding: ${user_cluster_access[$user]}"
          fi
        fi

        if [[ -n "$access_info" ]]; then
          echo "  👤 $user ($access_info)"
        else
          echo "  👤 $user (no current access)"
        fi
      done
    fi

    # --- Local certificate files section ----------------------------
    print_color "$BLUE" "🔍 Checking for local certificate files..."
    if [[ -d "user-certs" ]]; then
      local cert_files
      cert_files=$(find user-certs -maxdepth 1 -type f -name '*.crt' 2>/dev/null \
                   | sed -e 's|^user-certs/||' -e 's|\.crt$||')
      if [[ -n "$cert_files" ]]; then
        found_users=true
        print_color "$GREEN" "Users with local certificates:"
        for user in $cert_files; do
          local key_status="❌ Missing key"
          local kubeconfig_status="❌ No kubeconfig"
          [[ -f "user-certs/${user}.key" ]]      && key_status="✅ Key available"
          [[ -f "user-certs/${user}.kubeconfig" ]] && kubeconfig_status="✅ Kubeconfig available"
          echo "  📜 $user"
          echo "    - Private key: $key_status"
          echo "    - Kubeconfig:  $kubeconfig_status"

          # Check for viewer-role access only
          local user_ns
          user_ns=$(kubectl get rolebinding -A --no-headers 2>/dev/null \
                    | awk -v u="${user}-access" -v role="$DEFAULT_ROLE" '$2==u {
                        cmd="kubectl get rolebinding -n " $1 " " $2 " -o jsonpath=\"{.roleRef.name}\""
                        cmd | getline roleref
                        close(cmd)
                        if (roleref == role) print $1
                      }' | tr '\n' ' ')
          [[ -n "$user_ns" ]] && echo "    - ${DEFAULT_ROLE} access: $user_ns"

          # Check for ClusterRoleBinding access
          if check_user_in_cluster_binding "$user"; then
            echo "    - ClusterRoleBinding: ✅ $CLUSTER_ROLE_BINDING"
          else
            echo "    - ClusterRoleBinding: ❌ Not in $CLUSTER_ROLE_BINDING"
          fi
        done
      fi
    fi
  fi

  # 3) Summary ------------------------------------------------------
  echo
  if [[ "$found_users" == false ]]; then
    print_color "$YELLOW" "📭 No users found with ${DEFAULT_ROLE} access."
    print_color "$BLUE"  "💡 Either no users have been created yet or the cluster does not expose the User resource."
  else
    print_color "$GREEN" "📊 User discovery completed (${DEFAULT_ROLE} access only)!"
  fi

  echo
  read -p "Press Enter to continue..."
}

# Function to show detailed cluster information (for debugging)
show_cluster_info() {
    print_header "Cluster Information (Debug Mode)"

    print_color "$BLUE" "🔍 Kubernetes Cluster Context:"
    kubectl config current-context 2>/dev/null || print_color "$RED" "❌ No current context"

    echo
    print_color "$BLUE" "🔍 Available Namespaces:"
    kubectl get namespaces --no-headers 2>/dev/null | awk '{print "  - " $1 " (Age: " $3 ")"}'

    echo
    print_color "$BLUE" "🔍 Checking for ${DEFAULT_ROLE} in predefined namespaces:"
    for ns in "${PREDEFINED_NAMESPACES[@]}"; do
        echo "  Namespace: $ns"
        if kubectl get namespace "$ns" >/dev/null 2>&1; then
            if kubectl get role -n "$ns" "$DEFAULT_ROLE" >/dev/null 2>&1; then
                print_color "$GREEN" "    ✅ ${DEFAULT_ROLE} exists"
                # Show role permissions
                local permissions=$(kubectl get role -n "$ns" "$DEFAULT_ROLE" -o jsonpath='{.rules[*].verbs[*]}' 2>/dev/null | tr ' ' ',')
                [[ -n "$permissions" ]] && echo "       Permissions: $permissions"
            else
                print_color "$RED" "    ❌ ${DEFAULT_ROLE} not found"
            fi

            # Show other roles for reference
            local other_roles=$(kubectl get roles -n "$ns" --no-headers 2>/dev/null | awk -v target="$DEFAULT_ROLE" '$1!=target {print $1}')
            if [[ -n "$other_roles" ]]; then
                echo "    Other roles: $(echo "$other_roles" | tr '\n' ' ')"
            fi
        else
            print_color "$RED" "    ❌ Namespace does not exist"
        fi
        echo
    done

    echo
    print_color "$BLUE" "🔍 ClusterRoleBinding Information:"
    if kubectl get clusterrolebinding "$CLUSTER_ROLE_BINDING" >/dev/null 2>&1; then
        print_color "$GREEN" "✅ ClusterRoleBinding '$CLUSTER_ROLE_BINDING' exists"
        local cluster_role_ref=$(kubectl get clusterrolebinding "$CLUSTER_ROLE_BINDING" -o jsonpath='{.roleRef.name}' 2>/dev/null)
        echo "  - References ClusterRole: $cluster_role_ref"
        local user_count=$(kubectl get clusterrolebinding "$CLUSTER_ROLE_BINDING" -o jsonpath='{.subjects[?(@.kind=="User")].name}' 2>/dev/null | wc -w)
        echo "  - Current User subjects: $user_count"
    else
        print_color "$RED" "❌ ClusterRoleBinding '$CLUSTER_ROLE_BINDING' not found"
        print_color "$BLUE" "   Users will only get namespace-scoped access via RoleBindings"
    fi

    echo
    print_color "$BLUE" "🔍 Your current permissions:"
    echo "  Can create CSRs: $(kubectl auth can-i create csr 2>/dev/null && echo "✅ Yes" || echo "❌ No")"
    echo "  Can approve CSRs: $(kubectl auth can-i patch csr 2>/dev/null && echo "✅ Yes" || echo "❌ No")"
    echo "  Can create RoleBindings: $(kubectl auth can-i create rolebinding --all-namespaces 2>/dev/null && echo "✅ Yes" || echo "❌ No")"
    echo "  Can list all RoleBindings: $(kubectl auth can-i list rolebinding --all-namespaces 2>/dev/null && echo "✅ Yes" || echo "❌ No")"
    echo "  Can patch ClusterRoleBindings: $(kubectl auth can-i patch clusterrolebinding 2>/dev/null && echo "✅ Yes" || echo "❌ No")"

    echo
    read -p "Press Enter to continue..."
}

# Function to delete user
delete_user() {
    print_header "Delete User"

    local username
    username=$(get_username "Enter username to delete: ")

    # Check if user exists
    if ! check_user_exists "$username"; then
        print_color "$RED" "❌ User '$username' not found."
        read -p "Press Enter to continue..."
        return 1
    fi

    print_color "$BLUE" "🔍 Found user '$username'. Checking resources..."

    # Show what will be deleted
    echo
    print_color "$YELLOW" "The following resources will be deleted:"

    # Check CSR
    if kubectl get csr "${username}-csr" >/dev/null 2>&1; then
        echo "  - Certificate Signing Request: ${username}-csr"
    fi

    # Check local files
    if [[ -f "user-certs/${username}.crt" ]]; then
        echo "  - Certificate file: user-certs/${username}.crt"
    fi
    if [[ -f "user-certs/${username}.key" ]]; then
        echo "  - Private key file: user-certs/${username}.key"
    fi
    if [[ -f "user-certs/${username}.kubeconfig" ]]; then
        echo "  - Kubeconfig file: user-certs/${username}.kubeconfig"
    fi
    if [[ -f "user-certs/${username}.csr" ]]; then
        echo "  - CSR file: user-certs/${username}.csr"
    fi
    if [[ -f "user-certs/${username}.csr.conf" ]]; then
        echo "  - CSR config file: user-certs/${username}.csr.conf"
    fi

    # Check RoleBindings
    local rolebindings=$(kubectl get rolebinding --all-namespaces --no-headers 2>/dev/null | grep "${username}-access" | awk '{print $1 "/" $2}')
    if [[ -n "$rolebindings" ]]; then
        echo "  - RoleBindings:"
        for rb in $rolebindings; do
            echo "    - $rb"
        done
    fi

    # Check ClusterRoleBinding
    if check_user_in_cluster_binding "$username"; then
        echo "  - ClusterRoleBinding: $CLUSTER_ROLE_BINDING"
    fi

    echo
    if ! confirm_operation "This will permanently delete all resources for user '$username'."; then
        return 1
    fi

    print_color "$BLUE" "🗑️  Deleting user resources..."

    # Delete CSR
    if kubectl get csr "${username}-csr" >/dev/null 2>&1; then
        kubectl delete csr "${username}-csr" >/dev/null 2>&1
        print_color "$GREEN" "✅ Deleted CSR: ${username}-csr"
    fi

    # Delete RoleBindings
    for ns in "${PREDEFINED_NAMESPACES[@]}"; do
        if kubectl get rolebinding -n "$ns" "${username}-access" >/dev/null 2>&1; then
            kubectl delete rolebinding -n "$ns" "${username}-access" >/dev/null 2>&1
            print_color "$GREEN" "✅ Deleted RoleBinding: ${username}-access in namespace $ns"
        fi
    done

    # Remove user from ClusterRoleBinding
    if check_user_in_cluster_binding "$username"; then
        remove_user_from_cluster_binding "$username"
    fi

    # Delete local files
    local files_deleted=0
    for file in "user-certs/${username}.crt" "user-certs/${username}.key" "user-certs/${username}.kubeconfig" "user-certs/${username}.csr" "user-certs/${username}.csr.conf"; do
        if [[ -f "$file" ]]; then
            rm -f "$file"
            print_color "$GREEN" "✅ Deleted file: $file"
            ((files_deleted++))
        fi
    done

    echo
    print_color "$GREEN" "🎉 User '$username' has been successfully deleted!"
    print_color "$BLUE" "📊 Summary: Removed CSR, RoleBindings, and $files_deleted local files"

    echo
    read -p "Press Enter to continue..."
}

# Function to export kubeconfig for existing user
export_existing_kubeconfig() {
    print_header "Export Kubeconfig for Existing User"

    local username
    username=$(get_username "Enter username: ")

    # Check if user exists
    if ! check_user_exists "$username"; then
        print_color "$RED" "❌ User '$username' not found."
        read -p "Press Enter to continue..."
        return 1
    fi

    print_color "$GREEN" "✅ User '$username' found."

    # Create certs directory if it doesn't exist
    mkdir -p user-certs

    # Check if certificates exist locally
    if [[ ! -f "user-certs/${username}.crt" ]] || [[ ! -f "user-certs/${username}.key" ]]; then
        print_color "$YELLOW" "⚠️  Certificate files not found locally. Attempting to retrieve..."

        # Try to get certificate from existing CSR
        if kubectl get csr "${username}-csr" -o jsonpath='{.status.certificate}' &>/dev/null; then
            kubectl get csr "${username}-csr" -o jsonpath='{.status.certificate}' | base64 -d > "user-certs/${username}.crt"
            print_color "$GREEN" "✅ Retrieved certificate from cluster CSR."
        else
            print_color "$RED" "❌ Cannot retrieve certificate. CSR not found or not approved."
            read -p "Press Enter to continue..."
            return 1
        fi

        # Check for private key
        if [[ ! -f "user-certs/${username}.key" ]]; then
            print_color "$RED" "❌ Private key file 'user-certs/${username}.key' not found."
            print_color "$YELLOW" "Private keys cannot be retrieved from the cluster for security reasons."
            print_color "$BLUE" "Please ensure the private key file exists or recreate the user."
            read -p "Press Enter to continue..."
            return 1
        fi
    fi

    # Get user's current namespace access
    local user_namespaces=()
    for ns in "${PREDEFINED_NAMESPACES[@]}"; do
        if kubectl get rolebinding -n "$ns" "${username}-access" >/dev/null 2>&1; then
            user_namespaces+=("$ns")
        fi
    done

    if [[ ${#user_namespaces[@]} -eq 0 ]]; then
        print_color "$RED" "❌ User '$username' has no namespace access configured."
        read -p "Press Enter to continue..."
        return 1
    fi

    print_color "$BLUE" "📋 User has access to namespaces:"
    printf '  - %s\n' "${user_namespaces[@]}"

    # Generate kubeconfig
    generate_kubeconfig "$username" user_namespaces

    print_color "$GREEN" "🎉 Kubeconfig exported successfully!"
    print_color "$BLUE" "📁 File: user-certs/${username}.kubeconfig"
    print_color "$BLUE" "🧪 Test with: kubectl --kubeconfig=user-certs/${username}.kubeconfig get pods"

    echo
    read -p "Press Enter to continue..."
}

# Function to modify user access (RoleBindings)
modify_user_access() {
    print_header "Modify User Access (RoleBindings)"

    local username
    username=$(get_username "Enter username: ")

    # Check if user exists
    if ! check_user_exists "$username"; then
        print_color "$RED" "❌ User '$username' not found."
        read -p "Press Enter to continue..."
        return 1
    fi

    print_color "$GREEN" "✅ User '$username' found."

    # Show current access
    local current_namespaces=()
    for ns in "${PREDEFINED_NAMESPACES[@]}"; do
        if kubectl get rolebinding -n "$ns" "${username}-access" >/dev/null 2>&1; then
            current_namespaces+=("$ns")
        fi
    done

    echo
    if [[ ${#current_namespaces[@]} -gt 0 ]]; then
        print_color "$BLUE" "📋 Current namespace access:"
        printf '  - %s\n' "${current_namespaces[@]}"
    else
        print_color "$YELLOW" "⚠️  User has no current namespace access."
    fi

    # Ask for operation type
    echo
    print_color "$BLUE" "Select operation:"
    echo "  1) Grant access to additional namespaces"
    echo "  2) Revoke access from namespaces"
    echo "  3) Cancel"
    echo

    local choice
    while true; do
        read -p "Enter your choice (1-3): " choice
        case $choice in
            1)
                # Grant access
                echo
                print_color "$CYAN" "=== Grant Namespace Access (${DEFAULT_ROLE} only) ==="
                if ! get_namespace_selection "grant" "$username"; then
                    read -p "Press Enter to continue..."
                    return 1
                fi

                # Create RoleBindings for viewer-role only
                local granted=0
                for ns in "${VALID_NAMESPACES[@]}"; do
                    if kubectl get rolebinding -n "$ns" "${username}-access" >/dev/null 2>&1; then
                        print_color "$YELLOW" "⚠️  User already has access to namespace: $ns"
                    else
                        print_color "$BLUE" "🔗 Creating RoleBinding for ${DEFAULT_ROLE} in namespace: $ns"
                        cat <<EOF | kubectl apply -f - >/dev/null 2>&1
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ${username}-access
  namespace: ${ns}
subjects:
- kind: User
  name: ${username}
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: Role
  name: ${DEFAULT_ROLE}
  apiGroup: rbac.authorization.k8s.io
EOF
                        if [[ $? -eq 0 ]]; then
                            print_color "$GREEN" "✅ Granted access to namespace: $ns"
                            ((granted++))
                        else
                            print_color "$RED" "❌ Failed to grant access to namespace: $ns"
                        fi
                    fi
                done

                # Add user to ClusterRoleBinding
                print_color "$BLUE" "🌐 Adding user to ClusterRoleBinding..."
                add_user_to_cluster_binding "$username"

                echo
                print_color "$GREEN" "🎉 Access modification completed!"
                print_color "$BLUE" "📊 Granted access to $granted new namespaces"
                break
                ;;
            2)
                # Revoke access
                if [[ ${#current_namespaces[@]} -eq 0 ]]; then
                    print_color "$RED" "❌ User has no namespace access to revoke."
                    read -p "Press Enter to continue..."
                    return 1
                fi

                echo
                print_color "$CYAN" "=== Revoke Namespace Access (${DEFAULT_ROLE} only) ==="
                if ! get_namespace_selection "revoke" "$username"; then
                    read -p "Press Enter to continue..."
                    return 1
                fi

                # Confirm revocation
                echo
                if ! confirm_operation "This will revoke ${DEFAULT_ROLE} access to the selected namespaces for user '$username'."; then
                    return 1
                fi

                # Delete RoleBindings for viewer-role only
                local revoked=0
                for ns in "${VALID_NAMESPACES[@]}"; do
                    if kubectl get rolebinding -n "$ns" "${username}-access" >/dev/null 2>&1; then
                        kubectl delete rolebinding -n "$ns" "${username}-access" >/dev/null 2>&1
                        if [[ $? -eq 0 ]]; then
                            print_color "$GREEN" "✅ Revoked ${DEFAULT_ROLE} access from namespace: $ns"
                            ((revoked++))
                        else
                            print_color "$RED" "❌ Failed to revoke access from namespace: $ns"
                        fi
                    else
                        print_color "$YELLOW" "⚠️  User doesn't have ${DEFAULT_ROLE} access to namespace: $ns"
                    fi
                done

                # Ask if user should also be removed from ClusterRoleBinding
                echo
                if check_user_in_cluster_binding "$username"; then
                    print_color "$BLUE" "🌐 User is currently in ClusterRoleBinding '$CLUSTER_ROLE_BINDING'"
                    if confirm_operation "Remove user from ClusterRoleBinding as well?"; then
                        remove_user_from_cluster_binding "$username"
                    fi
                fi

                echo
                print_color "$GREEN" "🎉 Access modification completed!"
                print_color "$BLUE" "📊 Revoked access from $revoked namespaces"
                break
                ;;
            3)
                print_color "$BLUE" "Operation cancelled."
                read -p "Press Enter to continue..."
                return 0
                ;;
            *)
                print_color "$RED" "❌ Invalid choice. Please enter 1, 2, or 3."
                ;;
        esac
    done

    echo
    read -p "Press Enter to continue..."
}

# Function to generate kubeconfig
generate_kubeconfig() {
    local username="$1"
    local -n valid_namespaces_ref=$2

    echo "Generating kubeconfig for user '$username'..."

    # Get cluster information
    SERVER=$(kubectl config view --minify -o jsonpath='{.clusters[0].cluster.server}')
    CLUSTER_NAME=$(kubectl config view --minify -o jsonpath='{.clusters[0].name}')
    CA_DATA=$(kubectl config view --minify -o jsonpath='{.clusters[0].cluster.certificate-authority-data}')
    if [ -z "$CA_DATA" ]; then
        CA_DATA=$(kubectl get secrets -o jsonpath="{.items[?(@.metadata.annotations['kubernetes\.io/service-account\.name']=='default')].data['ca\.crt']}")
    fi

    # Use first namespace as default context
    DEFAULT_NAMESPACE="${valid_namespaces_ref[0]}"

    cat > "user-certs/${username}.kubeconfig" <<EOF
apiVersion: v1
kind: Config
clusters:
- cluster:
    server: ${SERVER}
    certificate-authority-data: ${CA_DATA}
  name: ${CLUSTER_NAME}
contexts:
- context:
    cluster: ${CLUSTER_NAME}
    user: ${username}
    namespace: ${DEFAULT_NAMESPACE}
  name: ${username}-context
current-context: ${username}-context
users:
- name: ${username}
  user:
    client-certificate-data: $(base64 -w0 "user-certs/${username}.crt")
    client-key-data: $(base64 -w0 "user-certs/${username}.key")
EOF
}

# Function to create new user
create_new_user() {
    print_header "Create New User"

    local username
    if [[ -n "$1" ]]; then
        username="$1"
        print_color "$BLUE" "Using provided username: $username"
    else
        username=$(get_username "Enter username for new user: ")
    fi

    # Check if user already exists
    if check_user_exists "$username"; then
        print_color "$RED" "❌ User '$username' already exists."
        print_color "$BLUE" "Use option 2 to export kubeconfig or option 3 to modify access."
        read -p "Press Enter to continue..."
        return 1
    fi

    print_color "$GREEN" "✅ Username '$username' is available."

    # Get namespace selection
    if ! get_namespace_selection "grant" "$username"; then
        read -p "Press Enter to continue..."
        return 1
    fi

    # Create certs directory
    mkdir -p user-certs

    print_color "$BLUE" "🔧 Creating new user '$username'..."

    # Generate private key
    print_color "$BLUE" "🔑 Generating private key..."
    openssl genrsa -out "user-certs/${username}.key" 2048 >/dev/null 2>&1

    # Create CSR config
    print_color "$BLUE" "📝 Creating CSR configuration..."
    cat > "user-certs/${username}.csr.conf" <<EOF
[ req ]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
[ dn ]
CN = ${username}
O = dev-group
[ v3_ext ]
authorityKeyIdentifier=keyid,issuer:always
basicConstraints=CA:FALSE
keyUsage=keyEncipherment,dataEncipherment
extendedKeyUsage=serverAuth,clientAuth
EOF

    # Generate CSR
    print_color "$BLUE" "📋 Generating Certificate Signing Request..."
    openssl req -new -key "user-certs/${username}.key" \
        -out "user-certs/${username}.csr" \
        -config "user-certs/${username}.csr.conf" >/dev/null 2>&1

    # Create Kubernetes CSR
    print_color "$BLUE" "☸️  Submitting CSR to Kubernetes..."
    cat <<EOF | kubectl apply -f - >/dev/null 2>&1
apiVersion: certificates.k8s.io/v1
kind: CertificateSigningRequest
metadata:
  name: ${username}-csr
spec:
  groups:
  - system:authenticated
  request: $(base64 -w0 < "user-certs/${username}.csr")
  signerName: kubernetes.io/kube-apiserver-client
  usages:
  - client auth
EOF

    # Approve CSR
    print_color "$BLUE" "✅ Approving Certificate Signing Request..."
    kubectl certificate approve "${username}-csr" >/dev/null 2>&1

    # Wait for certificate issuance
    print_color "$BLUE" "⏳ Waiting for certificate issuance..."
    for i in {1..10}; do
        if kubectl get csr "${username}-csr" -o jsonpath='{.status.certificate}' &>/dev/null; then
            break
        fi
        if [[ $i -eq 10 ]]; then
            print_color "$RED" "❌ Timeout waiting for certificate issuance"
            read -p "Press Enter to continue..."
            return 1
        fi
        sleep 1
    done

    # Retrieve certificate
    print_color "$BLUE" "📜 Retrieving signed certificate..."
    kubectl get csr "${username}-csr" -o jsonpath='{.status.certificate}' | base64 -d > "user-certs/${username}.crt"

    # Verify certificate exists
    if [ ! -s "user-certs/${username}.crt" ]; then
        print_color "$RED" "❌ Failed to get signed certificate for user"
        read -p "Press Enter to continue..."
        return 1
    fi

    # Create RoleBindings for viewer-role in each namespace
    print_color "$BLUE" "🔗 Creating RoleBindings for ${DEFAULT_ROLE}..."
    local created_bindings=0
    for ns in "${VALID_NAMESPACES[@]}"; do
        print_color "$BLUE" "  Creating ${DEFAULT_ROLE} RoleBinding in namespace: $ns"

        # Create RoleBinding for viewer-role only
        cat <<EOF | kubectl apply -f - >/dev/null 2>&1
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ${username}-access
  namespace: ${ns}
subjects:
- kind: User
  name: ${username}
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: Role
  name: ${DEFAULT_ROLE}
  apiGroup: rbac.authorization.k8s.io
EOF
        if [[ $? -eq 0 ]]; then
            print_color "$GREEN" "  ✅ ${DEFAULT_ROLE} RoleBinding created in namespace: $ns"
            ((created_bindings++))
        else
            print_color "$RED" "  ❌ Failed to create ${DEFAULT_ROLE} RoleBinding in namespace: $ns"
        fi
    done

    # Add user to ClusterRoleBinding
    print_color "$BLUE" "🌐 Adding user to ClusterRoleBinding..."
    add_user_to_cluster_binding "$username"

    # Generate kubeconfig for new user
    print_color "$BLUE" "📁 Generating kubeconfig..."
    generate_kubeconfig "$username" VALID_NAMESPACES

    echo
    print_color "$GREEN" "🎉 New user '$username' created successfully!"
    print_color "$BLUE" "📊 Summary:"
    print_color "$BLUE" "  - Certificate: ✅ Generated and signed"
    print_color "$BLUE" "  - ${DEFAULT_ROLE} RoleBindings: ✅ Created in $created_bindings namespaces"
    print_color "$BLUE" "  - ClusterRoleBinding: ✅ Added to $CLUSTER_ROLE_BINDING"
    print_color "$BLUE" "  - Kubeconfig: ✅ Generated"
    print_color "$BLUE" "📁 Kubeconfig file: user-certs/${username}.kubeconfig"
    print_color "$BLUE" "🔒 User has read-only access via ${DEFAULT_ROLE} Role and cluster-wide access"

    echo
    read -p "Press Enter to continue..."
}

# Main program logic
main() {
    # Check if username provided as argument (backward compatibility)
    if [[ -n "$1" ]]; then
        print_color "$BLUE" "🔄 Backward compatibility mode: Creating user with provided username"
        create_new_user "$1"
        return
    fi

    # Interactive menu mode
    while true; do
        show_main_menu
        choice=$(get_menu_choice)

        case $choice in
            1)
                create_new_user
                ;;
            2)
                export_existing_kubeconfig
                ;;
            3)
                modify_user_access
                ;;
            4)
                list_existing_users
                ;;
            5)
                delete_user
                ;;
            6)
                show_cluster_info
                ;;
            7)
                print_color "$GREEN" "👋 Thank you for using the Kubernetes User Management Tool!"
                exit 0
                ;;
        esac
    done
}

# Run main program
main "$@"